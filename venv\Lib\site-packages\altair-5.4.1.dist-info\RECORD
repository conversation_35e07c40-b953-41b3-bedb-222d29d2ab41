altair-5.4.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
altair-5.4.1.dist-info/METADATA,sha256=1VtjD94-tZcXV6Rv_VWR_6z-GJ4ro8J0HTiK1mpgUQ4,9391
altair-5.4.1.dist-info/RECORD,,
altair-5.4.1.dist-info/WHEEL,sha256=1yFddiXMmvYK7QYTqtRNtX66WJ0Mz8PYEiEUoOUUxRY,87
altair-5.4.1.dist-info/licenses/LICENSE,sha256=FL9avQqjkvgG-gu8iPUd42lk1TBxpodjHmIYB-VWMx4,1497
altair/__init__.py,sha256=LNPnTKfBl2izTC-DJm1vfmW-_CNn2B4mayGXCRmU6z0,16060
altair/__pycache__/__init__.cpython-38.pyc,,
altair/__pycache__/_magics.cpython-38.pyc,,
altair/__pycache__/typing.cpython-38.pyc,,
altair/_magics.py,sha256=7py96ydBwwJrM-0-6hGhBHvV6luee4j0kztcAt0azuY,3088
altair/expr/__init__.py,sha256=CytXhp5-5hpu4EzFTfIsy11o_3UgRhkzU6m19NCeU78,59938
altair/expr/__pycache__/__init__.cpython-38.pyc,,
altair/expr/__pycache__/consts.cpython-38.pyc,,
altair/expr/__pycache__/core.cpython-38.pyc,,
altair/expr/__pycache__/funcs.cpython-38.pyc,,
altair/expr/consts.py,sha256=N7wVV1jpKeEBEh4uyDObn4AYbARJeXKuEJIyM3wJChE,602
altair/expr/core.py,sha256=eR5-AN8eOnwWVfzYnVDWY4R0aWCP1zUkXZX8mBIsHsM,6892
altair/expr/funcs.py,sha256=BTeMDq-YFPSOKdx0A-y0VZr6Z1CywSYev6APnBKW68c,33826
altair/jupyter/__init__.py,sha256=vuW_WpL-i0-JaTKbDPTpOwQNvbDqO9Guk3Vej6PhH8A,876
altair/jupyter/__pycache__/__init__.cpython-38.pyc,,
altair/jupyter/__pycache__/jupyter_chart.cpython-38.pyc,,
altair/jupyter/js/README.md,sha256=iwzryhAUgm-HD7KupvKV2TKY5U74S7mLyGKe0rgB4D8,172
altair/jupyter/js/index.js,sha256=XVJuEOUTI9wyzVMCOsD6sbDhdVz8q7N89T6ajKIoYIg,7936
altair/jupyter/jupyter_chart.py,sha256=tiIl-g66bH5v5hFiG7W-WwoR_CSVS970P8PWwu7_j2A,15231
altair/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
altair/typing.py,sha256=H_5nflDS8JyPV35qZgvfoVDvv-97n7XXfPDDvU7615U,1956
altair/utils/__init__.py,sha256=430hWpEthlro2EHafopssERpXTEFbtrl9x01AbKzO3E,919
altair/utils/__pycache__/__init__.cpython-38.pyc,,
altair/utils/__pycache__/_dfi_types.cpython-38.pyc,,
altair/utils/__pycache__/_importers.cpython-38.pyc,,
altair/utils/__pycache__/_show.cpython-38.pyc,,
altair/utils/__pycache__/_transformed_data.cpython-38.pyc,,
altair/utils/__pycache__/_vegafusion_data.cpython-38.pyc,,
altair/utils/__pycache__/compiler.cpython-38.pyc,,
altair/utils/__pycache__/core.cpython-38.pyc,,
altair/utils/__pycache__/data.cpython-38.pyc,,
altair/utils/__pycache__/deprecation.cpython-38.pyc,,
altair/utils/__pycache__/display.cpython-38.pyc,,
altair/utils/__pycache__/execeval.cpython-38.pyc,,
altair/utils/__pycache__/html.cpython-38.pyc,,
altair/utils/__pycache__/mimebundle.cpython-38.pyc,,
altair/utils/__pycache__/plugin_registry.cpython-38.pyc,,
altair/utils/__pycache__/save.cpython-38.pyc,,
altair/utils/__pycache__/schemapi.cpython-38.pyc,,
altair/utils/__pycache__/selection.cpython-38.pyc,,
altair/utils/__pycache__/server.cpython-38.pyc,,
altair/utils/__pycache__/theme.cpython-38.pyc,,
altair/utils/_dfi_types.py,sha256=mxqUmoBvogcXAzAYiwaHy1SYMQ3T_cAZbnFwbYP_H2s,6375
altair/utils/_importers.py,sha256=G0kysQgqjUDnJuenAKz3vfdEddnb8sn0fAoL4KERRF4,3920
altair/utils/_show.py,sha256=OPM-OYCgf4BXl1eqjv-hjnZPY8Hq_hppXL14Bhtj-I0,2245
altair/utils/_transformed_data.py,sha256=ssvEKuvlxTuNKfXWKWkvRtPaNlLzLA7JKfk9aoNUffA,18146
altair/utils/_vegafusion_data.py,sha256=KRlhUFXvgPryo559U0lOMCsprz_93npU866WzoW0WXQ,8783
altair/utils/compiler.py,sha256=JrdIEtUmQU9ZjJAVQ1R138Vcu6rc21KSsq2R6JxDjEs,444
altair/utils/core.py,sha256=dLkuuEgxZ_VB33ppc_81JXYvsRu3KV1AuxmyTQE6VLM,31175
altair/utils/data.py,sha256=cXlXuivBKNZjUzcMTES1xxcOWOum37thp57kOEb_8Bo,14296
altair/utils/deprecation.py,sha256=76BlsrSE-xDm0_m_FrALY9xsOPxURE0X4FpXIYcRtxA,3582
altair/utils/display.py,sha256=bk0-AnTcigA6rRy0ZIfMHlgP5_5T9MZ_oUcOXuNv1-I,8543
altair/utils/execeval.py,sha256=lXqdObdYzG3-VbOXhIIhAI1o1NkDLfOgHSSVQEy7Xn0,1425
altair/utils/html.py,sha256=hLHrCMNFXd2GH6XgG7PbeurXYdgR9IjMEHpM4tl03K0,10149
altair/utils/mimebundle.py,sha256=uCMoosiXEeennzlaRqmFfuPlhOgS8G-g95S-IMCY1KM,12894
altair/utils/plugin_registry.py,sha256=zaV9ueNqrduH-cLa8Oh9rrhaqDEVJwJqGThz1X1_kNo,9705
altair/utils/save.py,sha256=x2lha_RQnW-4xymIW8FhQQswpanqGkrEt-HXcLw-lwE,8348
altair/utils/schemapi.py,sha256=1_wwvIAvUku6xFEIJxNeNLvorHrVFy697j7AvkXKdIs,56832
altair/utils/selection.py,sha256=22DO9lKH5X09PUxMCtMHpOQXk1aqmRVa3Yr-PhhO_ws,3940
altair/utils/server.py,sha256=74F8ob37E52e2VeJbdwqX40xUnCB_w5QC2IJJSqw7GE,4100
altair/utils/theme.py,sha256=O4pRIZnv8wDcxQKBdmkwumElyd9Pi-Knnfvfq70BoTw,1401
altair/vegalite/__init__.py,sha256=IZ_q0DdT8J4wGGXo1bPp001n44O8ns266ZphbWcImCw,37
altair/vegalite/__pycache__/__init__.cpython-38.pyc,,
altair/vegalite/__pycache__/api.cpython-38.pyc,,
altair/vegalite/__pycache__/data.cpython-38.pyc,,
altair/vegalite/__pycache__/display.cpython-38.pyc,,
altair/vegalite/__pycache__/schema.cpython-38.pyc,,
altair/vegalite/api.py,sha256=kZEoyq1nKhFNTcW6GRMyv7NYlQXqcpFuZ3m8eRApmEg,35
altair/vegalite/data.py,sha256=gyZ6VE2y7tj5ZUHQD-BSPb3N5hyDkV1pG2bUB10XTwA,1751
altair/vegalite/display.py,sha256=ULT1Z6vONlJCHAj8uqLVb9LCnPpD48J34Y8-aVeaZNU,342
altair/vegalite/schema.py,sha256=4UNGw-OO6_Es407m68k6JzTdc93rtnXl7AG-bVvgCtI,75
altair/vegalite/v5/__init__.py,sha256=VvlKMHejhujVDdWXBE8cVAmyR_QfqeSBm4L_W3AVmCY,447
altair/vegalite/v5/__pycache__/__init__.cpython-38.pyc,,
altair/vegalite/v5/__pycache__/api.cpython-38.pyc,,
altair/vegalite/v5/__pycache__/compiler.cpython-38.pyc,,
altair/vegalite/v5/__pycache__/data.cpython-38.pyc,,
altair/vegalite/v5/__pycache__/display.cpython-38.pyc,,
altair/vegalite/v5/__pycache__/theme.cpython-38.pyc,,
altair/vegalite/v5/api.py,sha256=S6l13wjMr621X0uq9I1dyBg7cUHC-37RRI30tgb1NMI,181922
altair/vegalite/v5/compiler.py,sha256=PmpPgBHocAACliN2riaW6lKVsTODfM6ekIRGAqKMsBM,816
altair/vegalite/v5/data.py,sha256=RRnY5kfcwUYgseXoTkJDdqUhFkEwdkKlvT8_yvpqAAw,1175
altair/vegalite/v5/display.py,sha256=B-RFtujx0cIbsAvAjaFB78MaHMdPTItQPrACWU0QXlA,5732
altair/vegalite/v5/schema/__init__.py,sha256=RaGF1xGBlu36T5GYDKfFz7PaBQD6RRdiNDea9m3U6mk,155
altair/vegalite/v5/schema/__pycache__/__init__.cpython-38.pyc,,
altair/vegalite/v5/schema/__pycache__/_typing.cpython-38.pyc,,
altair/vegalite/v5/schema/__pycache__/channels.cpython-38.pyc,,
altair/vegalite/v5/schema/__pycache__/core.cpython-38.pyc,,
altair/vegalite/v5/schema/__pycache__/mixins.cpython-38.pyc,,
altair/vegalite/v5/schema/_typing.py,sha256=JGKsLMfBI4yT1rcE13HxAnkgs8U88hgFJq8dJAwrw9k,23522
altair/vegalite/v5/schema/channels.py,sha256=fDU601jFMlC63YmzIoe6nth-TIlarYPvcihgVJBv7vQ,1259168
altair/vegalite/v5/schema/core.py,sha256=ORAQhhj9nRVFGOVIF_oWAEdkhmNQugb26bzZGaO6pEk,1556189
altair/vegalite/v5/schema/mixins.py,sha256=L6S--POePu5x83EMYQacR5dtYnoxlgk8vIWyV1UULTg,166515
altair/vegalite/v5/schema/vega-lite-schema.json,sha256=p_b2eXXLwF-Xcw2UpKFLONWbofLk8x4SuGblC0gRRt4,1847283
altair/vegalite/v5/schema/vega-themes.json,sha256=GB9RNXBPu-_Yk0hbYmggFr_sI9Vfsr4FIrJ4x1X1p3I,25451
altair/vegalite/v5/theme.py,sha256=30y2NQRiNrFkoef6ryeqcM3hN-GepfsDa1EoKEbPyyY,1725
