import streamlit as st
import pandas as pd
import os
import uuid
import json
import base64
import hmac
import hashlib
import time
import requests
from dotenv import load_dotenv
import io
import traceback
import logging
from datetime import datetime
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

# 设置日志
log_dir = "logs"
if not os.path.exists(log_dir):
    os.makedirs(log_dir)

log_file = os.path.join(log_dir, f"agent_responses_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
logging.basicConfig(
    filename=log_file,
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

# 加载环境变量
load_dotenv()

# 从环境变量加载配置信息
API_BASE_URL = os.getenv("API_BASE_URL", "https://www.das-ai.com")
APP_KEY = os.getenv("APP_KEY", "hengnaowueL9IzCaCTRADXvJZ1Y")
APP_SECRET = os.getenv("APP_SECRET", "frhozvq85j71fgvkxevrbzrfi29jkub1")
USER_APP_KEY = os.getenv("USER_APP_KEY", "hengnaoUK_QHPSL0yBd5DMOAiBXNW9")
AGENT_ID = os.getenv("AGENT_ID", "27544c91-5132-40b5-897e-22990038d051")

# 打印当前使用的配置信息
print(f"当前使用的API_BASE_URL: {API_BASE_URL}")
print(f"当前使用的AGENT_ID: {AGENT_ID}")

# 禁用代理设置
os.environ['HTTP_PROXY'] = ''
os.environ['HTTPS_PROXY'] = ''
os.environ['http_proxy'] = ''
os.environ['https_proxy'] = ''

# 设置请求超时时间（秒）- 调整为5分钟
REQUEST_TIMEOUT = 300

# 创建自定义会话以禁用代理并添加重试机制
def create_session():
    session = requests.Session()
    session.trust_env = False  # 不使用环境变量中的代理设置
    session.proxies = {
        'http': None,
        'https': None
    }
    
    # 配置重试策略
    retry_strategy = Retry(
        total=3,  # 最多重试3次
        status_forcelist=[429, 500, 502, 503, 504],  # 这些状态码会触发重试
        allowed_methods=["GET", "POST"],  # 允许GET和POST方法重试
        backoff_factor=1  # 重试间隔: {backoff factor} * (2 ** ({number of total retries} - 1))
    )
    
    adapter = HTTPAdapter(max_retries=retry_strategy)
    session.mount("http://", adapter)
    session.mount("https://", adapter)
    
    return session

# 生成HMACSHA256签名
def generate_signature():
    timestamp = str(int(time.time() * 1000))  # 当前时间戳（毫秒）
    
    # 签名生成公式：timestamp + "\n" + appSecret + "\n" + appKey
    message = f"{timestamp}\n{APP_SECRET}\n{APP_KEY}"
    
    # 计算HMACSHA256签名
    signature = hmac.new(
        APP_SECRET.encode('utf-8'),
        message.encode('utf-8'),
        hashlib.sha256
    ).digest()
    
    # Base64编码签名
    signature_b64 = base64.b64encode(signature).decode('utf-8')
    
    return timestamp, signature_b64

# 生成认证头
def get_auth_headers(content_type="application/json"):
    timestamp, signature = generate_signature()
    
    # 构建认证头
    headers = {
        "appKey": APP_KEY,
        "sign": f"{timestamp}{signature}",
        "Content-Type": content_type,
        "Accept": "application/octet-stream"  # 添加Accept头部，用于文件下载
    }
    
    return headers

# 文件上传
class FileBucket:
    def __init__(self):
        self.session = create_session()
        
    def put(self, filename, binary_data):
        """上传文件到平台的文件存储服务"""
        upload_url = f"{API_BASE_URL}/open/api/v2/agent/file/upload"
        
        # 对于文件上传，需要特殊处理headers
        timestamp, signature = generate_signature()
        
        headers = {
            "appKey": APP_KEY,
            "sign": f"{timestamp}{signature}"
            # 对于multipart/form-data，不要设置Content-Type，requests会自动设置正确的值
        }
        
        try:
            files = {
                'file': (os.path.basename(filename.split('<')[0] if '<' in filename else filename), binary_data)
            }
            # 记录到后台日志
            logging.info(f"正在上传文件: {filename}, 大小: {len(binary_data)} 字节")
            
            # 发送请求，增加超时设置，使用自定义会话
            response = self.session.post(
                upload_url, 
                headers=headers, 
                files=files,
                timeout=REQUEST_TIMEOUT,
                verify=True  # SSL验证
            )
            
            response_data = response.json()
            
            if response.status_code == 200 and response_data.get("code") == 0:
                return response_data["data"]  # 直接返回文件ID字符串
            else:
                error_msg = f"文件上传失败: 状态码={response.status_code}, 响应={response_data}"
                logging.error(error_msg)  # 保留后台日志
                print(error_msg)  # 保留控制台输出
                return None
        except Exception as e:
            error_msg = f"文件上传异常: {str(e)}\n{traceback.format_exc()}"
            logging.error(error_msg)  # 保留后台日志
            print(error_msg)  # 保留控制台输出
            return None
            
    def get(self, file_id):
        """从平台获取文件内容"""
        
        # 仅在后台记录日志
        logging.info(f"正在下载文件，ID: {file_id}")
        print(f"正在下载文件，ID: {file_id}")
        
        # 使用正确的下载API路径
        download_url = f"{API_BASE_URL}/open/api/v2/agent/file/download"
        
        # 确保文件ID格式正确
        original_file_id = file_id  # 保存原始文件ID用于日志
        if file_id and isinstance(file_id, str):
            # 从file:xxx<yyy>格式中提取实际的文件ID (yyy部分)
            if file_id.startswith("file:") and "<" in file_id and ">" in file_id:
                try:
                    import re
                    match = re.search(r'file:[^<]*<([^>]+)>', file_id)
                    if match:
                        actual_file_id = match.group(1)
                        logging.info(f"从file:格式中提取实际文件ID: {actual_file_id}")
                        print(f"从file:格式中提取实际文件ID: {actual_file_id}")
                        # 使用实际文件ID
                        file_id = actual_file_id
                except Exception as e:
                    logging.warning(f"从file:格式提取文件ID失败: {str(e)}")
            elif file_id.startswith("file:"):
                # 如果是file:xxx格式但没有<>，尝试去掉file:前缀
                file_id = file_id[5:]
                logging.info(f"去除file:前缀后的文件ID: {file_id}")
        
        # 严格按照API文档设置请求头
        timestamp, signature = generate_signature()
        methods_to_try = [
            {
                "name": "严格按照API文档",
                "headers": {
                    "appKey": APP_KEY,
                    "sign": f"{timestamp}{signature}",
                    "Content-Type": "application/json",
                    "Accept": "application/octet-stream"
                }
            }
        ]
        
        for method in methods_to_try:
            try:
                headers = method["headers"]
                method_name = method["name"]
                
                # 详细记录请求信息
                debug_info = f"尝试 {method_name}\n" \
                           f"请求URL: {download_url}\n" \
                           f"原始文件ID: {original_file_id}\n" \
                           f"处理后文件ID: {file_id}\n" \
                           f"请求头: {headers}"
                
                logging.info(debug_info)
                print(debug_info)
                # st.session_state.debug_info += f"\n{debug_info}" # 移除前端调试信息
                
                # 根据API文档示例处理文件ID
                # 保留完整的file:xxx<yyy>格式
                import re
                processed_file_id = file_id
                
                # 确保文件ID格式正确
                if isinstance(file_id, str):
                    # 如果已经是file:xxx<yyy>格式，直接使用
                    if file_id.startswith("file:") and "<" in file_id and ">" in file_id:
                        processed_file_id = file_id
                        logging.info(f"文件ID已经是正确格式: {processed_file_id}")
                    # 如果只是UUID，尝试从原始文件ID中提取文件名
                    elif re.match(r'^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$', file_id, re.IGNORECASE):
                        # 从原始文件ID中提取文件名
                        filename_match = re.search(r'file:([^<]+)', original_file_id)
                        filename = filename_match.group(1) if filename_match else "document.docx"
                        processed_file_id = f"file:{filename}<{file_id}>"
                        logging.info(f"将UUID转换为完整格式: {file_id} -> {processed_file_id}")
                
                # 确保ID干净无空格
                clean_file_id = processed_file_id.strip()
                
                # 记录处理后的文件ID
                logging.info(f"最终处理后的文件ID: '{file_id}' -> '{clean_file_id}'")
                print(f"最终处理后的文件ID: '{file_id}' -> '{clean_file_id}'")
                
                # 创建payload，严格按照API文档格式
                payload = {"fileId": clean_file_id}
                
                # 在终端打印完整的请求体
                import json
                formatted_payload = json.dumps(payload, ensure_ascii=False, indent=2)
                print(f"\n发送的完整请求体:\n{formatted_payload}\n")
                logging.info(f"发送的完整请求体: {formatted_payload}")
                # st.session_state.debug_info += f"\n发送的完整请求体:\n{formatted_payload}" # 移除前端调试信息
                
                # 记录完整的请求信息
                request_detail = f"请求体: {json.dumps(payload, ensure_ascii=False)}"
                logging.info(request_detail)
                print(request_detail)
                # st.session_state.debug_info += f"\n{request_detail}" # 移除前端调试信息
                
                # 根据API文档，只使用正确的API路径
                api_paths = [
                    "/open/api/v2/agent/file/download"  # API文档指定的路径
                ]
                
                for api_path in api_paths:
                    current_url = f"{API_BASE_URL}{api_path}"
                    logging.info(f"尝试API路径: {current_url}")
                    print(f"尝试API路径: {current_url}")
                    
                    try:
                        # 记录详细的请求信息
                        request_log = f"\n==== 文件下载请求 ====\n" \
                                     f"URL: {current_url}\n" \
                                     f"请求头: {headers}\n" \
                                     f"请求体: {json.dumps(payload, ensure_ascii=False)}\n" \
                                     f"========================"
                        print(request_log)
                        logging.info(request_log)
                        
                        # 发送请求
                        response = self.session.post(
                            current_url, 
                            headers=headers, 
                            json=payload,
                            timeout=REQUEST_TIMEOUT,
                            verify=True
                        )
                        
                        # 记录详细的响应信息
                        response_log = f"\n==== 文件下载响应 ====\n" \
                                      f"状态码: {response.status_code}\n" \
                                      f"响应头: {dict(response.headers)}\n"
                        
                        # 尝试获取响应内容信息
                        content_type = response.headers.get('Content-Type', '')
                        content_length = response.headers.get('Content-Length', '未知')
                        
                        if 'application/json' in content_type:
                            try:
                                json_response = response.json()
                                response_log += f"JSON响应: {json.dumps(json_response, ensure_ascii=False, indent=2)}\n"
                            except:
                                response_log += f"响应内容: 无法解析的JSON\n"
                        else:
                            response_log += f"内容类型: {content_type}\n" \
                                           f"内容长度: {content_length} 字节\n" \
                                           f"内容预览: {response.content[:100].hex() if len(response.content) > 0 else '空'}\n"
                        
                        response_log += f"========================"
                        print(response_log)
                        logging.info(response_log)
                        # st.session_state.debug_info += response_log # 移除前端调试信息
                        
                        # 检查响应
                        if response.status_code == 200 and len(response.content) > 0:
                            if 'application/json' not in content_type:
                                # 如果不是JSON响应，可能是成功的文件内容
                                logging.info(f"API路径 {api_path} 成功返回文件内容")
                                break
                            else:
                                # 检查JSON响应是否表示成功
                                try:
                                    json_response = response.json()
                                    if json_response.get('code') == 0:
                                        logging.info(f"API路径 {api_path} 返回成功的JSON响应")
                                        break
                                except:
                                    pass
                        
                        logging.warning(f"API路径 {api_path} 未返回有效响应，尝试下一个路径")
                    except Exception as e:
                        error_log = f"API路径 {api_path} 请求失败: {str(e)}\n{traceback.format_exc()}"
                        logging.error(error_log)
                        print(error_log)
                        # st.session_state.debug_info += f"\n{error_log}" # 移除前端调试信息
                        continue
                
                # 记录响应信息
                response_info = f"响应状态码: {response.status_code}\n" \
                              f"响应头: {dict(response.headers)}\n" \
                              f"内容类型: {response.headers.get('Content-Type', '未知')}\n" \
                              f"内容长度: {response.headers.get('Content-Length', '未知')}"
                
                logging.info(response_info)
                print(response_info)
                # st.session_state.debug_info += f"\n{response_info}" # 移除前端调试信息
                
                # 检查响应状态
                if response.status_code == 200:
                    # 获取内容
                    content = response.content
                    
                    # 检查内容类型
                    content_type = response.headers.get('Content-Type', '')
                    if 'application/json' in content_type:
                        try:
                            # 尝试解析JSON响应
                            json_response = response.json()
                            json_info = f"收到JSON响应: {json_response}"
                            logging.warning(json_info)
                            print(json_info)
                            # st.session_state.debug_info += f"\n{json_info}" # 移除前端调试信息
                            
                            # 如果是错误消息，继续尝试下一个方法
                            if json_response.get('code') != 0:
                                continue
                        except:
                            pass
                    
                    # 检查内容是否为空
                    if content and len(content) > 0:
                        # 记录成功信息
                        success_info = f"文件下载成功! 方法: {method_name}, 大小: {len(content)} 字节"
                        if len(content) > 50:
                            success_info += f", 前50字节: {content[:50].hex()}"
                        
                        logging.info(success_info)
                        print(success_info)
                        # st.session_state.debug_info += f"\n{success_info}" # 移除前端调试信息
                        
                        # 尝试自动保存文件到磁盘（仅用于调试）
                        try:
                            # 从文件ID提取文件名
                            import re
                            match = re.match(r'file:([^<]+)', original_file_id)
                            auto_filename = match.group(1) if match else "downloaded_file.docx"
                            
                            # 确保文件名有效
                            auto_filename = "".join([c for c in auto_filename if c.isalnum() or c in "._- "])
                            if not auto_filename:
                                auto_filename = "downloaded_file.docx"
                            
                            # 创建下载目录
                            download_dir = "downloads"
                            if not os.path.exists(download_dir):
                                os.makedirs(download_dir)
                            
                            # 保存文件
                            save_path = os.path.join(download_dir, auto_filename)
                            with open(save_path, "wb") as f:
                                f.write(content)
                            
                            save_info = f"文件已自动保存到: {save_path}"
                            logging.info(save_info)
                            print(save_info)
                            # st.session_state.debug_info += f"\n{save_info}" # 移除前端调试信息
                        except Exception as e:
                            logging.error(f"自动保存文件失败: {str(e)}")
                        
                        return content
                    else:
                        logging.warning(f"方法 {method_name} 返回空内容，尝试下一个方法")
                        continue
                else:
                    error_msg = f"方法 {method_name} 失败: 状态码={response.status_code}, 响应={response.text}"
                    logging.error(error_msg)
                    print(error_msg)
                    # st.session_state.debug_info += f"\n{error_msg}" # 移除前端调试信息
                    continue
            except Exception as e:
                error_msg = f"方法 {method_name} 异常: {str(e)}\n{traceback.format_exc()}"
                logging.error(error_msg)
                print(error_msg)
                # st.session_state.debug_info += f"\n{error_msg}" # 移除前端调试信息
                continue
        
        # 所有方法都失败
        logging.error("所有下载方法均失败")
        return None

# 调用智能体服务
def call_agent(file_id, user_input, additional_params=None):
    """调用平台智能体服务"""
    agent_url = f"{API_BASE_URL}/open/api/v2/agent/execute"
    
    # 获取认证头
    headers = get_auth_headers()
    
    # 获取会话ID（如果存在）或创建新的
    if 'session_id' not in st.session_state:
        st.session_state.session_id = str(uuid.uuid4())
    
    # 准备结构化输入参数
    inputs = {
        "file": file_id,
        "input": user_input
    }
    
    # 合并额外参数
    if additional_params:
        inputs.update(additional_params)
    
    # 准备请求体
    payload = {
        "id": AGENT_ID,                   # 智能体ID
        "sid": st.session_state.session_id,  # 会话ID
        "inputs": inputs                  # 结构化输入
    }
    
    try:
        # 显示详细的请求信息（仅用于调试）
        request_info = f"\n正在调用智能体: {agent_url}\n" \
                      f"请求头: {headers}\n" \
                      f"请求体: {payload}"
        # st.session_state.debug_info += request_info # 移除前端调试信息
        print(request_info)
        
        # 记录开始时间，用于计算请求耗时
        start_time = time.time()
        logging.info(f"开始调用智能体，时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 使用无代理会话
        session = create_session()
        response = session.post(
            agent_url, 
            headers=headers, 
            json=payload,
            timeout=REQUEST_TIMEOUT,
            verify=True  # SSL验证
        )
        
        # 计算请求耗时
        elapsed_time = time.time() - start_time
        logging.info(f"智能体调用完成，耗时: {elapsed_time:.2f}秒")
        print(f"智能体调用完成，耗时: {elapsed_time:.2f}秒")
        
        response_data = response.json()
        
        # 记录智能体响应到日志文件
        logging.info(f"用户输入: {user_input}")
        logging.info(f"智能体响应: {json.dumps(response_data, ensure_ascii=False, indent=2)}")
        
        # 在控制台打印智能体响应
        print("\n" + "="*50)
        print(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"用户输入: {user_input}")
        print(f"智能体响应: {json.dumps(response_data, ensure_ascii=False, indent=2)}")
        print("="*50 + "\n")
        
        if response.status_code == 200 and response_data.get("code") == 0:
            return response_data
        else:
            error_message = f"错误代码: {response_data.get('code')}, 消息: {response_data.get('message', '未知错误')}"
            # st.session_state.debug_info += f"\n调用智能体失败: {error_message}" # 移除前端调试信息
            print(f"调用智能体失败: {error_message}")
            logging.error(f"调用智能体失败: {error_message}")
            return {"error": error_message}
    except requests.exceptions.Timeout:
        error_msg = f"调用智能体超时 (>{REQUEST_TIMEOUT}秒)。可能是因为文件较大或网络问题，请稍后重试。"
        # st.session_state.debug_info += f"\n{error_msg}" # 移除前端调试信息
        print(error_msg)
        logging.error(error_msg)
        return {"error": error_msg}
    except Exception as e:
        error_msg = f"调用智能体异常: {str(e)}\n{traceback.format_exc()}"
        # st.session_state.debug_info += f"\n{error_msg}" # 移除前端调试信息
        print(error_msg)
        logging.error(error_msg)
        return {"error": str(e)}

# 从响应中提取文件ID（如果有）和output内容
def extract_response_data(response):
    """从智能体响应中提取文件ID和output内容"""
    file_id = None
    output_content = None
    
    try:
        # 首先检查整个响应中是否有merged_file字段(直接在消息内容中)
        if response and isinstance(response, dict):
            # 直接在响应中寻找merged_file字段
            if "merged_file" in str(response):
                # 尝试在不同层级找到merged_file
                if response.get("merged_file"):
                    file_id = response.get("merged_file")
                    logging.info(f"在根级别找到merged_file: {file_id}")
        
        # 检查data.results字段中是否有output和merged_file
        if response and response.get("data"):
            # 检查results字段
            if response["data"].get("results"):
                # 提取output
                if response["data"]["results"].get("output"):
                    output_content = response["data"]["results"]["output"]
                    logging.info(f"从results中提取到output内容: {output_content}")
                    print(f"从results中提取到output内容: {output_content}")
                    
                    # 检查output内容中是否包含merged_file
                    if isinstance(output_content, str) and "merged_file" in output_content:
                        try:
                            # 尝试解析output中的JSON
                            parsed_output = json.loads(output_content)
                            if parsed_output and parsed_output.get("merged_file"):
                                file_id = parsed_output["merged_file"]
                                logging.info(f"从output内容中提取到merged_file: {file_id}")
                                print(f"从output内容中提取到merged_file: {file_id}")
                        except:
                            # 使用正则表达式提取
                            try:
                                import re
                                match = re.search(r'"merged_file"\s*:\s*"(file:[^"]+)"', output_content)
                                if match:
                                    file_id = match.group(1)
                                    logging.info(f"使用正则从output内容中提取到merged_file: {file_id}")
                                    print(f"使用正则从output内容中提取到merged_file: {file_id}")
                            except Exception as e:
                                logging.error(f"从output提取merged_file出错: {str(e)}")
                                pass
                
                # 提取merged_file
                if response["data"]["results"].get("merged_file"):
                    file_id = response["data"]["results"]["merged_file"]
                    logging.info(f"从results中提取到文件ID: {file_id}")
                    print(f"从results中提取到文件ID: {file_id}")
        
        # 检查session.messages中的内容
        if not output_content and response and response.get("data") and response["data"].get("session") and response["data"]["session"].get("messages"):
            messages = response["data"]["session"]["messages"]
            if messages and len(messages) > 0:
                # 获取助手的回复（通常是最后一条消息）
                for msg in reversed(messages):
                    if msg.get("role") == "assistant":
                        msg_content = msg.get("content", "")
                        
                        # 首先尝试解析为JSON
                        try:
                            parsed_content = json.loads(msg_content)
                            
                            # 检查是否有merged_file字段
                            if parsed_content and parsed_content.get("merged_file"):
                                file_id = parsed_content["merged_file"]
                                logging.info(f"从消息内容JSON中提取到merged_file: {file_id}")
                                print(f"从消息内容JSON中提取到merged_file: {file_id}")
                            
                            # 检查是否有output字段
                            if parsed_content and parsed_content.get("output"):
                                output_content = parsed_content["output"]
                                logging.info(f"从消息内容JSON中提取到output: {output_content}")
                                print(f"从消息内容JSON中提取到output: {output_content}")
                            
                            # 如果没有output但有其他内容，可能整个JSON就是要显示的内容
                            if not output_content and not parsed_content.get("merged_file"):
                                output_content = msg_content
                        except json.JSONDecodeError:
                            # 不是JSON，检查是否包含文件ID
                            if "merged_file" in msg_content or "file:" in msg_content:
                                import re
                                # 匹配 "merged_file":"file:xxx" 或 "file:xxx" 格式
                                match = re.search(r'"merged_file"\s*:\s*"(file:[^"]+)"|file:([^<\s"]+)', msg_content)
                                if match:
                                    file_id = match.group(1) or match.group(2)
                                    logging.info(f"通过正则从文本中提取到文件ID: {file_id}")
                                    print(f"通过正则从文本中提取到文件ID: {file_id}")
                            
                            # 如果没有文件ID，将消息内容作为output
                            if not output_content and msg_content:
                                output_content = msg_content
                                if msg_content != "null" and len(msg_content.strip()) > 0:
                                    logging.info(f"使用消息内容作为output: {output_content}")
                                    print(f"使用消息内容作为output: {output_content}")
                        
                        # 找到一个助手消息后就退出循环
                        break
    except Exception as e:
        error_msg = f"提取响应数据异常: {str(e)}\n{traceback.format_exc()}"
        logging.error(error_msg)
        print(error_msg)
    
    # 确保文件ID格式正确
    if file_id and not file_id.startswith("file:"):
        # 如果文件ID不是以file:开头，添加前缀
        if "<" in file_id:
            # 文件ID格式可能已经包含文件名
            file_id = f"file:{file_id}"
        else:
            # 如果没有文件名，添加一个默认名称
            file_id = f"file:downloaded_file.docx<{file_id}>"
    
    return file_id, output_content

# 直接处理JSON字符串的函数
def process_raw_json_response(response_text):
    """直接处理原始JSON字符串响应，提取文件ID和内容"""
    try:
        # 检查是否是有效的JSON字符串
        if isinstance(response_text, str) and response_text.strip().startswith("{") and response_text.strip().endswith("}"):
            try:
                # 尝试解析JSON
                json_obj = json.loads(response_text)
                
                # 检查是否有merged_file字段
                if "merged_file" in json_obj:
                    file_id = json_obj["merged_file"]
                    logging.info(f"从JSON对象中直接提取merged_file: {file_id}")
                    print(f"从JSON对象中直接提取merged_file: {file_id}")
                    
                    # 可能还有output字段
                    output_content = json_obj.get("output", "文件已生成")
                    
                    return file_id, output_content
                
                return None, None
            except json.JSONDecodeError:
                # 不是有效的JSON，尝试使用正则表达式
                pass
        
        # 使用正则表达式匹配
        if "merged_file" in response_text:
            import re
            match = re.search(r'"merged_file"\s*:\s*"(file:[^"]+)"', response_text)
            if match:
                file_id = match.group(1)
                logging.info(f"使用正则表达式从字符串中提取merged_file: {file_id}")
                print(f"使用正则表达式从字符串中提取merged_file: {file_id}")
                
                # 尝试提取output内容，如果没有就使用默认消息
                output_match = re.search(r'"output"\s*:\s*"([^"]+)"', response_text)
                output_content = output_match.group(1) if output_match else "文件已生成"
                
                return file_id, output_content
        
        return None, None
    except Exception as e:
        error_msg = f"处理原始JSON响应异常: {str(e)}\n{traceback.format_exc()}"
        logging.error(error_msg)
        print(error_msg)
        return None, None

# Streamlit应用
st.set_page_config(
    page_title="城管智能体——道路病害智能服务助手", 
    layout="wide",
    menu_items=None
)

# 隐藏右上角的Deploy按钮和菜单，并增大字体
st.markdown("""
<style>
    .stDeployButton {display:none;}
    footer {visibility: hidden;}
    .stDecoration {display:none;}
    header[data-testid="stHeader"] {display: none;}
    .viewerBadge_container__1QSob {display: none;}
    .styles_viewerBadge__1yB5_ {display: none;}
    #MainMenu {visibility: hidden;}
    
    /* 增大全局字体大小 */
    .main .block-container {
        font-size: 18px !important;
    }
    
    /* 增大标题字体 */
    h1 {
        font-size: 3.0rem !important;
        font-weight: 600 !important;
    }
    
    h2 {
        font-size: 2.5rem !important;
        font-weight: 500 !important;
    }
    
    h3 {
        font-size: 2.0rem !important;
        font-weight: 500 !important;
    }
    
    /* 增大聊天消息字体 */
    .stChatMessage {
        font-size: 25px !important;
    }
    
    .stChatMessage p,
    .stChatMessage li,
    .stChatMessage div:not([class*="stMarkdown"]) {
        font-size: 25px !important;
        line-height: 1.6 !important;
    }

    /* 保持标题的特殊大小 */
    .stChatMessage h3[level="3"],
    .stChatMessage h3,
    [data-testid="stChatMessage"] h3,
    [data-testid="stMarkdownContainer"] h3 {
        font-size: 2.5rem !important;
        font-weight: 600 !important;
        margin: 1rem 0 0.5rem 0 !important;
    }

    /* 同样处理其他标题级别 */
    .stChatMessage h2[level="2"],
    .stChatMessage h2,
    [data-testid="stChatMessage"] h2,
    [data-testid="stMarkdownContainer"] h2 {
        font-size: 3rem !important;
        font-weight: 600 !important;
    }

    .stChatMessage h1[level="1"],
    .stChatMessage h1,
    [data-testid="stChatMessage"] h1,
    [data-testid="stMarkdownContainer"] h1 {
        font-size: 3.5rem !important;
        font-weight: 600 !important;
    }

    /* 保持粗体的特殊样式 */
    .stChatMessage strong,
    .stChatMessage b {
        font-weight: bold !important;
        font-size: inherit !important; /* 继承父元素字体大小 */
    }
    
    .stChatMessage p {
        font-size: 25px !important;
        line-height: 1.6 !important;
    }
    
    /* 增大按钮字体 */
    .stButton button {
        font-size: 60px !important;
        font-weight: 500 !important;
    }
    
    /* 增大输入框字体 */
    .stTextInput input {
        font-size: 20px !important;
    }
    
    .stTextArea textarea {
        font-size: 20px !important;
    }
    
    /* 增大侧边栏字体 */
    .css-1d391kg {
        font-size: 25px !important;
    }
    
    /* 增大信息框字体 */
    .stInfo {
        font-size: 25px !important;
    }
    
    .stSuccess {
        font-size: 25px !important;
    }
    
    .stError {
        font-size: 25px !important;
    }
    
    .stWarning {
        font-size: 25px !important;
    }
    
    /* 增大展开器字体 */
    .streamlit-expanderHeader {
        font-size: 18px !important;
        font-weight: 500 !important;
    }
    
    .streamlit-expanderContent {
        font-size: 16px !important;
    }
    
    /* 增大下载按钮字体 */
    .stDownloadButton button {
        font-size: 16px !important;
        font-weight: 500 !important;
    }
    
    /* 增大文件上传组件字体 */
    [data-testid="stFileUploader"] {
        font-size: 16px !important;
    }
    
    /* 增大spinner文字 */
    .stSpinner > div {
        font-size: 16px !important;
    }
    
    /* 扩大聊天消息中的头像大小 */
    [data-testid="stChatMessage"] img {
        width: 60px !important;
        height: 60px !important;
        border-radius: 50% !important;
    }

    /* 调整头像容器的大小 */
    [data-testid="stChatMessage"] [data-testid="chatAvatarIcon"] {
        width: 60px !important;
        height: 60px !important;
    }

    /* 如果需要调整头像与消息内容的间距 */
    [data-testid="stChatMessage"] {
        gap: 1rem !important;
    }

    /* 增加聊天输入框的高度 */
    [data-testid="stChatInput"] textarea {
        min-height: 80px !important;
        height: 80px !important;
        padding: 16px !important;
    }

    /* 调整输入框容器的高度 */
    [data-testid="stChatInput"] {
        min-height: 80px !important;
    }

    /* 如果需要调整输入框的字体大小 */
    [data-testid="stChatInput"] textarea {
        font-size: 18px !important;
        line-height: 1.5 !important;
    }
</style>
""", unsafe_allow_html=True)


# 初始化会话状态
if 'messages' not in st.session_state:
    st.session_state.messages = []
    # 添加初始问候语
    st.session_state.messages.append({
        "role": "assistant",
        "content": "你好呀，我是道路病害智能服务助手，请在左侧上传一个文件，我可以帮助你分析病害情况。"
    })

if 'uploaded_file_id' not in st.session_state:
    st.session_state.uploaded_file_id = None
if 'is_first_upload' not in st.session_state:
    st.session_state.is_first_upload = True
if 'second_message_sent' not in st.session_state:
    st.session_state.second_message_sent = False
if 'auto_question' not in st.session_state:
    st.session_state.auto_question = None

# 添加一个标志来跟踪是否正在处理请求
if 'processing_request' not in st.session_state:
    st.session_state.processing_request = False
if 'pending_user_input' not in st.session_state:
    st.session_state.pending_user_input = None
if 'show_uploader' not in st.session_state:
    st.session_state.show_uploader = False

# 主界面：文件上传和聊天
st.title("城管智能体——道路病害智能服务助手")

# 侧边栏：文件上传
with st.sidebar:
    st.subheader("上传XLSX文件")
    st.caption("请上传一个Excel文件")
    
    # 如果已经上传了文件，显示文件信息和删除按钮
    if st.session_state.uploaded_file_id:
        # 显示文件信息
        st.success(f"✅ 已上传文件")
        
        # 获取文件名
        if 'uploaded_file_name' not in st.session_state:
            st.session_state.uploaded_file_name = "未知文件.xlsx"
        
        # 显示文件名
        st.info(f"📄 文件名: {st.session_state.uploaded_file_name}")
        
        # 添加继续上传按钮
        if st.button("📁 继续上传", type="primary", use_container_width=True):
            # 临时显示文件上传组件，但保持当前文件信息
            st.session_state.show_uploader = True
            # 重置首次上传标志，确保新文件可以被处理
            st.session_state.is_first_upload = True
            st.rerun()
        
        # 删除文件按钮
        if st.button("🗑️ 删除文件", type="secondary", use_container_width=True):
            # 清除文件相关的会话状态
            st.session_state.uploaded_file_id = None
            st.session_state.uploaded_file_name = None
            st.session_state.is_first_upload = True
            st.session_state.second_message_sent = False
            st.session_state.show_uploader = False
            
            # 清除聊天历史（可选）
            if st.checkbox("同时清除聊天记录", value=True):
                st.session_state.messages = []
                st.session_state.messages.append({
                    "role": "assistant",
                    "content": "你好呀，我是道路病害智能服务助手，请在左侧上传一个文件，我可以帮助你分析病害情况。"
                })
            
            st.rerun()
    
    # 显示文件上传组件的条件：没有上传文件 或者 点击了继续上传
    if not st.session_state.uploaded_file_id or st.session_state.get('show_uploader', False):
        # 没有上传文件时，显示文件上传组件
        # 添加自定义CSS来修改文件上传组件的英文文本
        st.markdown("""
        <style>
        /* 保持原有的CSS样式 */
        [data-testid="stFileUploader"] * {
            font-family: inherit !important;
        }
        
        [data-testid="stFileUploader"] *:not(button):not(button *):not(small):not(small *):not(label):not(label *):not([data-testid*="fileUploadDropzone"]):not([data-testid*="fileUploadDropzone"] *) {
            color: transparent !important;
            font-size: 0 !important;
        }
            /* 隐藏"Limit 200MB per file"文本 */
        [data-testid="stFileUploader"] small {
            font-size: 0 !important;
            color: transparent !important;
        }
        
        /* 添加中文文件大小限制文本 */
        [data-testid="stFileUploader"] small::before {
            content: "单个文件限制200MB" !important;
            font-size: 12px !important;
            color: #6c757d !important;
            display: block !important;
        }
        
        [data-testid="stFileUploader"] button {
            background-color: rgb(240,242,246) !important;
            color: rgb(240,242,246) !important;
            border: none !important;
            border-radius: 6px !important;
            padding: 8px 64px !important;
            font-size: 0 !important;
            font-weight: 500 !important;
            cursor: pointer !important;
            transition: background-color 0.3s !important;
            position: relative !important;
            overflow: hidden !important;
        }
        
        [data-testid="stFileUploader"] button * {
            font-size: 0 !important;
            color: transparent !important;
            visibility: hidden !important;
            opacity: 0 !important;
        }
        
        [data-testid="stFileUploader"] button::after {
            content: "上传文件" !important;
            position: absolute !important;
            top: 0 !important;
            left: 0 !important;
            right: 0 !important;
            bottom: 0 !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            font-size: 14px !important;
            color: black !important;
            font-weight: 500 !important;
            visibility: visible !important;
            opacity: 1 !important;
            z-index: 999 !important;
        }
        [data-testid="stFileUploader"] button:hover {
            background-color: gray !important;
            background: gray !important;
            border: 1px solid #ccc !important;
        }
        </style>
        """, unsafe_allow_html=True)
        
        uploaded_file = st.file_uploader("选择文件", type=["xlsx"], key="file_uploader")
        
        # 处理文件上传
        if uploaded_file is not None:
            if st.session_state.is_first_upload or st.button("处理上传的文件", key="process_upload"):
                # 重置首次上传标志
                st.session_state.is_first_upload = False
                
                # 显示上传进度
                with st.spinner(f"正在上传文件: {uploaded_file.name}..."):
                    # 获取文件二进制内容
                    file_bytes = uploaded_file.getvalue()
                    
                    # 保存文件名到会话状态
                    st.session_state.uploaded_file_name = uploaded_file.name
                    
                    # 上传文件
                    fb = FileBucket()
                    file_id = fb.put(uploaded_file.name, file_bytes)
                    
                    if file_id:
                        # 保存文件ID到会话状态
                        st.session_state.uploaded_file_id = file_id
                        st.session_state.show_uploader = False  # 隐藏上传组件
                        st.success(f"文件上传成功! 文件名:{uploaded_file.name}")
                        
                        # 记录日志
                        logging.info(f"文件上传成功，文件名: {uploaded_file.name}")
                        print(f"文件上传成功，文件名: {uploaded_file.name}")
                        
                        # 添加第二条固定消息（只在第一次上传时）
                        if not st.session_state.second_message_sent:
                            st.session_state.messages.append({
                                "role": "assistant",
                                "content": "文件上传成功，请问你有什么需要询问的？"
                            })
                            st.session_state.second_message_sent = True
                        else:
                            # 继续上传时的提示消息
                            st.session_state.messages.append({
                                "role": "assistant",
                                "content": f"新文件 {uploaded_file.name} 上传成功，请继续提问。"
                            })
                        
                        # 立即刷新页面以显示文件信息
                        st.rerun()
                    else:
                        st.error("文件上传失败，请重试")
                        logging.error("文件上传失败")
                        print("文件上传失败")

    # 添加使用说明到侧边栏
    st.markdown("---")
    with st.expander("使用说明"):
        st.markdown("""
        ### 使用方法：
        1. 在上方上传一个XLSX文件
        2. 在右侧输入框中输入您的问题
        3. 系统会调用智能体处理您的问题并给出回复
        4. 如果智能体回复包含文件，会提供下载按钮
        
        ### 注意事项：
        - 上传的文件会在整个会话过程中保持可用
        - 每次对话都会将您上传的文件发送给智能体
        - 会话历史会被保存，直到页面刷新
        """)

# 移除调试信息显示区域
# with st.expander("调试信息", expanded=False):
#     if st.button("清除调试信息", key="clear_debug"):
#         st.session_state.debug_info = ""
#     st.text_area("调试日志", value=st.session_state.debug_info, height=300)

# 显示当前上传的文件信息
# if st.session_state.uploaded_file_id:
#     st.success(f"当前已上传文件，文件名: {uploaded_file.name}")

# 创建固定高度的聊天容器
chat_container = st.container(height=650)

# 只在这一个地方显示所有消息
with chat_container:
    for i, message in enumerate(st.session_state.messages):
        with st.chat_message(message["role"], avatar="images/城管.png" if message["role"] == "assistant" else None):
        #将None改为"images/用户.png"
            st.write(message["content"])
            
            # 如果消息包含文件ID，提供下载按钮
            if message["role"] == "assistant" and message.get("file_id"):
                file_id = message["file_id"]
                try:
                    if ':' in file_id and '<' in file_id:
                        filename = file_id.split(':')[1].split('<')[0]
                    else:
                        filename = "生成的文件"
                        
                    # 创建FileBucket实例并预先获取文件内容
                    fb = FileBucket()
                    file_content = fb.get(file_id)
                    
                    if file_content and len(file_content) > 0:
                        st.download_button(
                            "✅ 下载文件",
                            data=file_content,
                            file_name=filename,
                            mime="application/octet-stream",
                            key=f"download_history_{i}_{hash(file_id)}"
                        )
                except Exception as e:
                    logging.error(f"显示历史文件下载按钮出错: {str(e)}")
    
    # 如果有待处理的请求，在容器内显示"让助手跑一会..."
    if st.session_state.processing_request and st.session_state.pending_user_input:
        with st.chat_message("assistant", avatar="images/城管.png"):
            with st.spinner("让助手跑一会..."):
                # 调用智能体
                max_retries = 2  # 最多重试1次（总共调用2次）
                retry_count = 0
                file_id = None
                assistant_response = ""
                
                while retry_count < max_retries:
                    try:
                        response = call_agent(st.session_state.uploaded_file_id, st.session_state.pending_user_input)
                        
                        if "error" in response:
                            error_message = response["error"]
                            
                            # 检查是否是需要重试的错误
                            if ("错误代码: -504" in error_message and "未知错误" in error_message) or \
                               ("错误代码: -504" in error_message):
                                retry_count += 1
                                if retry_count < max_retries:
                                    # 只在后台记录重试信息，不显示给用户
                                    logging.info(f"检测到-504错误，正在进行第{retry_count + 1}次重试...")
                                    print(f"检测到-504错误，正在进行第{retry_count + 1}次重试...")
                                    time.sleep(1)  # 等待1秒后重试
                                    continue
                                else:
                                    # 多次重试失败，显示最终错误（不显示重试信息）
                                    assistant_response = f"调用智能体出错: {error_message}"
                                    break
                            else:
                                # 其他错误不重试，直接显示
                                assistant_response = f"调用智能体出错: {error_message}"
                                break
                        else:
                            file_id, output_content = extract_response_data(response)
                            
                            # 如果有文件ID，显示友好的提示信息
                            if file_id:
                                assistant_response = "报告生成完毕，点击下方按钮下载文件"
                            else:
                                assistant_response = output_content if output_content else "智能体未返回有效回复"
                            
                            break  # 成功获取回复，退出重试循环
                        
                    except Exception as e:
                        error_message = str(e)
                        
                        # 检查是否是网络相关错误，可以重试
                        if any(keyword in error_message.lower() for keyword in ["timeout", "connection", "network", "502", "503", "504"]):
                            retry_count += 1
                            if retry_count < max_retries:
                                # 只在后台记录重试信息，不显示给用户
                                logging.info(f"检测到网络错误，正在进行第{retry_count + 1}次重试: {error_message}")
                                print(f"检测到网络错误，正在进行第{retry_count + 1}次重试: {error_message}")
                                time.sleep(1)  # 等待1秒后重试
                                continue
                            else:
                                # 多次重试失败，显示最终错误（不显示重试信息）
                                assistant_response = f"调用智能体出错: {error_message}"
                                break
                        else:
                            # 其他异常不重试，直接显示
                            assistant_response = f"调用智能体出错: {error_message}"
                            break
            
            # 在spinner结束后，显示打字机效果
            if assistant_response:
                # 创建一个占位符用于显示逐渐出现的文本
                message_placeholder = st.empty()
                displayed_text = ""
                
                # 逐字显示文本
                for char in assistant_response:
                    displayed_text += char
                    message_placeholder.write(displayed_text)
                    time.sleep(0.02)  # 0.02秒延迟
                
                # 如果有文件，显示下载按钮
                if file_id:
                    try:
                        if ':' in file_id and '<' in file_id:
                            filename = file_id.split(':')[1].split('<')[0]
                        else:
                            filename = "生成的文件"
                            
                        # 创建FileBucket实例并获取文件内容
                        fb = FileBucket()
                        file_content = fb.get(file_id)
                        
                        if file_content and len(file_content) > 0:
                            st.download_button(
                                "✅ 下载文件",
                                data=file_content,
                                file_name=filename,
                                mime="application/octet-stream",
                                key=f"download_current_{hash(file_id)}"
                            )
                    except Exception as e:
                        logging.error(f"显示文件下载按钮出错: {str(e)}")
                
                # 添加真正的助手回复到历史记录
                st.session_state.messages.append({
                    "role": "assistant", 
                    "content": assistant_response,
                    "file_id": file_id if file_id else None
                })
                
                # 清除处理标志
                st.session_state.processing_request = False
                st.session_state.pending_user_input = None
                
                # 刷新显示最终结果
                st.rerun()

# 处理快速提问（在容器外部）
if hasattr(st.session_state, 'auto_question') and st.session_state.auto_question:
    user_input = st.session_state.auto_question
    
    # 不添加用户消息到历史记录，直接处理
    if user_input == "道路病害类型一共有哪些？":
        st.session_state.messages.append({"role": "user", "content": "介绍道路病害类型"})  
    elif user_input == "每个片区的病害面积情况和数量情况如何？":
        st.session_state.messages.append({"role": "user", "content": "介绍片区情况"})
    elif user_input == "病害数量最多的前五个道路是什么":
        st.session_state.messages.append({"role": "user", "content": "阐述一下道路情况top5"})
    elif user_input == "帮我生成一个报告":
        st.session_state.messages.append({"role": "user", "content": "请生成分析报告"})
    
    # 清除自动问题
    st.session_state.auto_question = None
    
    # 设置处理标志和待处理输入（与手动输入使用相同机制）
    st.session_state.processing_request = True
    st.session_state.pending_user_input = user_input
    
    # 重新运行以显示新消息
    st.rerun()

# 处理手动输入（在容器外部）
if st.session_state.uploaded_file_id:
    user_input = st.chat_input("请输入您的问题...")
    
    if user_input and not st.session_state.processing_request:
        # 立即添加用户消息到历史
        st.session_state.messages.append({"role": "user", "content": user_input})
        
        # 设置处理标志和待处理输入
        st.session_state.processing_request = True
        st.session_state.pending_user_input = user_input
        
        # 立即刷新显示用户输入
        st.rerun()# 添加快速提问按钮 - 使用Streamlit原生按钮
    st.markdown("---")
    st.caption("💡 快速提问")

    st.markdown("""
    <style>
    /* 更精确的选择器，针对快速提问按钮 */
    div[data-testid="column"] button[kind="primary"],
    div[data-testid="column"] button[kind="secondary"],
    div[data-testid="column"] .stButton > button {
        font-size: 24px !important;
        font-weight: 600 !important;
        padding: 15px 12px !important;
        height: auto !important;
        min-height: 45px !important;
        line-height: 1.2 !important;
    }
    
    /* 强制修改按钮内部文字 */

    div[data-testid="column"] .stButton > button *,
    div[data-testid="column"] button span,
    div[data-testid="column"] button p,
    div[data-testid="column"] button div {
        font-size: 24px !important;
        font-weight: 600 !important;
    }
    
    /* 备用选择器 */
    .stButton button {
        font-size: 24px !important;
        font-weight: 600 !important;
        padding: 20px 16px !important;
        height: auto !important;
        min-height: 60px !important;
    }
    
    .stButton button * {
        font-size: 24px !important;
        font-weight: 600 !important;
    }
    </style>
    """, unsafe_allow_html=True)

    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        if st.button("🛣️ 道路病害类型", use_container_width=True, key="quick_btn_1"):
            st.session_state.auto_question = "道路病害类型一共有哪些？"
            st.rerun()
    
    with col2:
        if st.button("📊 片区情况", use_container_width=True, key="quick_btn_2"):
            st.session_state.auto_question = "每个片区的病害面积情况和数量情况如何？"
            st.rerun()
    
    with col3:
        if st.button("❗道路情况top5", use_container_width=True, key="quick_btn_3"):
            st.session_state.auto_question = "病害数量最多的前五个道路是什么"
            st.rerun()

    with col4:
        if st.button("📁 生成分析报告", use_container_width=True, key="quick_btn_4"):
            st.session_state.auto_question = "帮我生成一个报告"
            st.rerun()

else:
    pass

# 添加使用说明 

# 添加一个函数用于直接下载文件
def direct_download_file(file_id):
    """直接下载文件并返回内容，用于简化下载流程"""
    try:
        # 只在后台记录日志，不在前端显示
        logging.info(f"直接下载函数被调用! 文件ID: {file_id}")
        
        # 创建FileBucket实例
        fb = FileBucket()
        
        # 获取文件内容
        file_content = fb.get(file_id)
        
        # 返回文件内容
        return file_content
    except Exception as e:
        logging.error(f"直接下载出错: {str(e)}\n{traceback.format_exc()}")
        return None
