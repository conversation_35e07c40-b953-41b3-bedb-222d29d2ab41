
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>智能体服务调用</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #333;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="file"], input[type="text"], textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
        #result {
            margin-top: 20px;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 4px;
            background-color: #f9f9f9;
            white-space: pre-wrap;
            display: none;
        }
        .spinner {
            border: 4px solid rgba(0, 0, 0, 0.1);
            width: 36px;
            height: 36px;
            border-radius: 50%;
            border-left-color: #09f;
            animation: spin 1s linear infinite;
            display: none;
            margin: 20px auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .info {
            background-color: #e7f3fe;
            border-left: 6px solid #2196F3;
            padding: 10px;
            margin-bottom: 15px;
        }
        #downloadBtn { display: none; margin-top: 10px; }
    </style>
</head>
<body>
    <h1>安恒恒脑智能体服务</h1>
    
    <div class="info">
        <p>当前调用的智能体ID: <strong>{{ agent_id }}</strong></p>
    </div>
    
    <form id="uploadForm">
        <div class="form-group">
            <label for="file">上传文件:</label>
            <input type="file" id="file" name="file" required>
        </div>
        <div class="form-group">
            <label for="image">上传图片:</label>
            <input type="file" id="image" name="image" accept="image/*" required>
        </div>
        <div class="form-group">
            <label for="param1">附加参数1 (可选):</label>
            <input type="text" id="param1" name="param1">
        </div>
        <div class="form-group">
            <label for="param2">附加参数2 (可选):</label>
            <input type="text" id="param2" name="param2">
        </div>
        <button type="submit">调用智能体</button>
    </form>
    
    <div class="spinner" id="spinner"></div>
    <pre id="result"></pre>
    <button id="downloadBtn">下载生成的文件</button>

    <script>
        let lastFileId = null;
        document.getElementById('uploadForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const formData = new FormData();
            const fileInput = document.getElementById('file');
            const imageInput = document.getElementById('image');
            const param1 = document.getElementById('param1').value;
            const param2 = document.getElementById('param2').value;
            
            if (!fileInput.files[0] || !imageInput.files[0]) {
                alert('请选择文件和图片');
                return;
            }
            
            formData.append('file', fileInput.files[0]);
            formData.append('image', imageInput.files[0]);
            
            if (param1) formData.append('param1', param1);
            if (param2) formData.append('param2', param2);
            
            const spinner = document.getElementById('spinner');
            const resultElem = document.getElementById('result');
            const downloadBtn = document.getElementById('downloadBtn');
            
            spinner.style.display = 'block';
            resultElem.style.display = 'none';
            downloadBtn.style.display = 'none';
            
            try {
                const response = await fetch('/upload', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                resultElem.textContent = JSON.stringify(result, null, 2);
                resultElem.style.display = 'block';
                
                // 检查是否有output_doc字段
                let fileId = null;
                if (result && result.data && result.data.session && result.data.session.messages && result.data.session.messages.length > 0) {
                    try {
                        const msg = result.data.session.messages[0].content;
                        const parsed = JSON.parse(msg);
                        if (parsed.output_doc) fileId = parsed.output_doc;
                    } catch (e) {}
                }
                if (fileId) {
                    lastFileId = fileId;
                    downloadBtn.style.display = 'inline-block';
                } else {
                    lastFileId = null;
                    downloadBtn.style.display = 'none';
                }
            } catch (error) {
                resultElem.textContent = '调用出错: ' + error.message;
                resultElem.style.display = 'block';
                downloadBtn.style.display = 'none';
            } finally {
                spinner.style.display = 'none';
            }
        });
        document.getElementById('downloadBtn').addEventListener('click', function() {
            if (lastFileId) {
                window.open('/download?file_id=' + encodeURIComponent(lastFileId), '_blank');
            }
        });
    </script>
</body>
</html>
    