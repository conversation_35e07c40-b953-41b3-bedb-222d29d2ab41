2025-07-22 16:16:03,982 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,007 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,008 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,008 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,008 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,008 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,008 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,008 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,008 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,008 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,008 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,008 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,008 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,008 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,008 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,008 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,008 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,013 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,013 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,013 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,013 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,014 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,014 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,014 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,014 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,014 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,015 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,015 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,016 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,016 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,016 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,016 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,016 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,017 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,017 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,017 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,017 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,017 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,018 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,018 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,019 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,019 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,019 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,019 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,020 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,020 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,020 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,020 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,020 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,021 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,021 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,021 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,022 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,022 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,022 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,022 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,022 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,022 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,022 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,023 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,023 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,023 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,023 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,023 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,024 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,024 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,024 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,024 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,024 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,024 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,025 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,025 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,026 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,026 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,026 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,026 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,026 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,026 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,026 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,026 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,026 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,028 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,028 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,028 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,028 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,028 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,028 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,028 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,028 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,028 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,028 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,028 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,028 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,028 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,028 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,028 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,028 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,028 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,028 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,028 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,028 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,028 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,028 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,028 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,028 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,028 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,028 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,028 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,028 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,028 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,028 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,028 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,028 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,028 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,028 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,028 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,028 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,028 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,028 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,028 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,028 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,028 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,028 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,028 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,028 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,028 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,028 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,028 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,028 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,038 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,038 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,038 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,038 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,039 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,039 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,039 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,040 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,040 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,040 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,040 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,040 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,041 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,041 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,041 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,041 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,041 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,041 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,041 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,041 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,041 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,041 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,041 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,041 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,041 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,041 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,041 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,041 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,041 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,041 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,041 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,041 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,041 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,041 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,041 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,041 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,041 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,041 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,041 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,041 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,041 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,041 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,041 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,041 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,041 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,048 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,048 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,048 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,048 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,048 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,048 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,048 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,048 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,048 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,048 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,048 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,048 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,048 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,048 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,048 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,048 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,048 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,048 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,048 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,048 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,048 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,048 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,048 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,048 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,048 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,053 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,053 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,053 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,053 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,053 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,053 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,053 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,053 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,053 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,053 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,053 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,053 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,056 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,056 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,056 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,056 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,056 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,056 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,056 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,056 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,057 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,057 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,058 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,058 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,058 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,058 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,058 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,059 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,059 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,059 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,059 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,060 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,060 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,060 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,060 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,060 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,060 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,060 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,060 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,060 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,060 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,060 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,060 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,060 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,060 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,060 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,060 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,060 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,060 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,060 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,060 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,060 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,060 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,060 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,060 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,060 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,068 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,068 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,068 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,068 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,068 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,068 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,068 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,068 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,068 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,068 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,068 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,068 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,068 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,068 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,068 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,068 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,068 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,068 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,068 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,068 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,068 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,068 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,068 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,068 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,068 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,073 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,073 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,073 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,073 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,073 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,073 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,073 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,073 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,073 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,073 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,076 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,076 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,076 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,076 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,076 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,076 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,076 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,076 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,078 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,078 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,078 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,078 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,078 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,078 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,078 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,078 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,078 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,078 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,078 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,078 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,078 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,078 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,078 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,078 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,078 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,078 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,078 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,078 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,078 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,078 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,078 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,078 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,083 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,083 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,083 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,083 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,083 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,083 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,083 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,083 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,083 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,083 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,083 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,083 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,083 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,083 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,083 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,083 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,083 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,083 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,083 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,083 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,083 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,083 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,083 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,083 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,088 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,088 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,088 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,088 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,088 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,088 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,088 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,088 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,088 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,088 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,088 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,088 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,088 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,088 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,088 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,088 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,088 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,088 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,088 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,092 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,092 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,092 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,092 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,092 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,092 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,092 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,092 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,092 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,092 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,092 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,092 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,092 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,092 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,092 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,092 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,092 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,092 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,092 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,092 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,092 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,092 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,092 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,092 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,092 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,092 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,092 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,092 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,092 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,098 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,098 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,099 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,099 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,099 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,099 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,099 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,099 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,099 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,099 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
2025-07-22 16:16:04,100 - ERROR - Exception in callback BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536
handle: <Handle BaseIOStream.write.<locals>.<lambda>(<Future cancelled>) at e:\shixi2\venv\lib\site-packages\tornado\iostream.py:536>
Traceback (most recent call last):
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "e:\shixi2\venv\lib\site-packages\tornado\iostream.py", line 536, in <lambda>
    future.add_done_callback(lambda f: f.exception())
asyncio.exceptions.CancelledError
