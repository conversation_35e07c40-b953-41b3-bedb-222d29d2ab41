openapi: 3.0.0
info:
  title: 高德地图天气查询API
  description: 使用高德地图API查询城市天气信息
  version: 1.0.0
servers:
  - url: https://restapi.amap.com
paths:
  /v3/weather/weatherInfo:
    get:
      summary: 查询城市天气
      description: 根据城市名称或编码查询实时天气信息
      operationId: getWeather
      parameters:
        - name: key
          in: query
          description: 高德开放平台的应用key
          required: true
          schema:
            type: string
        - name: city
          in: query
          description: 城市编码或城市名称
          required: true
          schema:
            type: string
        - name: extensions
          in: query
          description: 气象类型，可选值：base/all，base:返回实况天气，all:返回预报天气
          required: false
          schema:
            type: string
            enum: [base, all]
            default: base
        - name: output
          in: query
          description: 返回格式，可选值：JSON/XML
          required: false
          schema:
            type: string
            enum: [JSON, XML]
            default: JSON
      responses:
        '200':
          description: 成功返回天气信息
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    description: 返回状态，1表示成功
                  count:
                    type: string
                    description: 返回结果数目
                  info:
                    type: string
                    description: 返回的状态信息
                  infocode:
                    type: string
                    description: 返回状态码
                  lives:
                    type: array
                    description: 实况天气数据
                    items:
                      type: object
                      properties:
                        province:
                          type: string
                          description: 省份名
                        city:
                          type: string
                          description: 城市名
                        adcode:
                          type: string
                          description: 区域编码
                        weather:
                          type: string
                          description: 天气现象
                        temperature:
                          type: string
                          description: 实时温度
                        winddirection:
                          type: string
                          description: 风向
                        windpower:
                          type: string
                          description: 风力级别
                        humidity:
                          type: string
                          description: 空气湿度
                        reporttime:
                          type: string
                          description: 数据发布时间
                  forecasts:
                    type: array
                    description: 预报天气数据
                    items:
                      type: object
                      properties:
                        city:
                          type: string
                        adcode:
                          type: string
                        province:
                          type: string
                        reporttime:
                          type: string
                        casts:
                          type: array
                          items:
                            type: object
                            properties:
                              date:
                                type: string
                              week:
                                type: string
                              dayweather:
                                type: string
                              nightweather:
                                type: string
                              daytemp:
                                type: string
                              nighttemp:
                                type: string
                              daywind:
                                type: string
                              nightwind:
                                type: string
                              daypower:
                                type: string
                              nightpower:
                                type: string